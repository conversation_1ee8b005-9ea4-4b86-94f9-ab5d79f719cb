using System.IO.Compression;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Records bars to disk for back-test replay with Zstd compression
/// </summary>
internal sealed class BarRecorder : IBarRecorder
{
    private readonly ILogger<BarRecorder> _logger;
    private readonly ICompressionCircuitBreakerService _compressionCircuitBreaker;
    private readonly object _gate = new();
    private readonly string _root = Path.Combine("Data", "Bars");

    public BarRecorder(
        ILogger<BarRecorder> logger,
        ICompressionCircuitBreakerService compressionCircuitBreaker)
    {
        _logger = logger;
        _compressionCircuitBreaker = compressionCircuitBreaker;
        
        // Ensure root directory exists
        Directory.CreateDirectory(_root);
        
        _logger.LogInformation("BarRecorder initialized with root directory: {RootPath}", Path.GetFullPath(_root));
    }

    /// <summary>
    /// Records bars to disk in CSV format with background Zstd compression
    /// </summary>
    /// <param name="bars">Collection of bars to record</param>
    /// <param name="symbol">Symbol name</param>
    /// <param name="tfUtc">Time frame key (e.g., "Day", "Minute")</param>
    public void Record(IEnumerable<IBar> bars, string symbol, string tfUtc)
    {
        var barsList = bars.ToList();
        if (!barsList.Any())
        {
            _logger.LogDebug("No bars to record for {Symbol} {TimeFrame}", symbol, tfUtc);
            return;
        }

        try
        {
            var day = barsList.First().TimeUtc.Date;
            var dir = Path.Combine(_root, day.ToString("yyyy"), day.ToString("MM"), day.ToString("dd"));
            Directory.CreateDirectory(dir);

            var file = Path.Combine(dir, $"{symbol}_{tfUtc}.csv");
            var tmp = file + ".tmp";

            _logger.LogDebug("Recording {Count} bars for {Symbol} {TimeFrame} to {File}", 
                barsList.Count, symbol, tfUtc, file);

            // Single-threaded file access to prevent corruption
            lock (_gate)
            {
                using var writer = new StreamWriter(new FileStream(tmp, FileMode.Append, FileAccess.Write, FileShare.None));
                
                foreach (var bar in barsList)
                {
                    // Write CSV format: TimeUtc,Open,High,Low,Close,Volume
                    writer.WriteLine($"{bar.TimeUtc:O},{bar.Open},{bar.High},{bar.Low},{bar.Close},{bar.Volume}");
                }
            }

            // Compress in background to avoid blocking the main thread
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // Small delay to ensure file is fully written

                    // Check circuit breaker before attempting compression
                    if (!_compressionCircuitBreaker.ShouldAttemptCompression())
                    {
                        _logger.LogDebug("Compression circuit breaker open, skipping compression for {Symbol} {TimeFrame}",
                            symbol, tfUtc);
                        return;
                    }

                    // Validate source file before compression
                    if (!File.Exists(tmp))
                    {
                        _logger.LogWarning("Temporary file {TempFile} not found for compression, skipping", tmp);
                        return;
                    }

                    var fileInfo = new FileInfo(tmp);
                    if (fileInfo.Length == 0)
                    {
                        _logger.LogWarning("Temporary file {TempFile} is empty, skipping compression", tmp);
                        File.Delete(tmp); // Clean up empty file
                        return;
                    }

                    var compressedFile = file + ".zst";
                    _logger.LogDebug("Starting compression: {TempFile} ({Size:N0} bytes) -> {CompressedFile}",
                        tmp, fileInfo.Length, compressedFile);

                    ZstdUtils.CompressAndReplace(tmp, compressedFile);

                    // Record successful compression
                    _compressionCircuitBreaker.RecordSuccess();

                    // Clean up temporary file only after successful compression
                    if (File.Exists(tmp))
                    {
                        File.Delete(tmp);
                        _logger.LogDebug("Successfully compressed and cleaned up {TempFile} -> {CompressedFile}",
                            tmp, compressedFile);
                    }
                }
                catch (Exception ex)
                {
                    // Record compression failure
                    _compressionCircuitBreaker.RecordFailure(symbol, tfUtc, ex);

                    _logger.LogError(ex, "Failed to compress bar data for {Symbol} {TimeFrame}: {Error}. " +
                                       "Temporary file preserved: {TempFile}",
                        symbol, tfUtc, ex.Message, tmp);

                    // Don't delete the temp file on compression failure - preserve data
                    // This allows manual recovery or investigation
                    _logger.LogInformation("Temporary file {TempFile} preserved for manual recovery", tmp);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record bars for {Symbol} {TimeFrame}", symbol, tfUtc);
        }
    }
}
