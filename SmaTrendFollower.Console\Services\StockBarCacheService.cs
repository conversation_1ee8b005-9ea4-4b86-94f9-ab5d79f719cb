using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Configuration;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// SQLite-based implementation of stock bar caching service.
/// Provides efficient caching of Alpaca/Polygon stock data to minimize API calls.
/// Implements 1-year retention with differential updates.
/// </summary>
public sealed class StockBarCacheService : IStockBarCacheService
{
    private readonly StockBarCacheDbContext _dbContext;
    private readonly ILogger<StockBarCacheService> _logger;

    public StockBarCacheService(StockBarCacheDbContext dbContext, ILogger<StockBarCacheService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<IReadOnlyList<IBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        try
        {
            // Validate inputs to prevent database errors
            if (string.IsNullOrWhiteSpace(symbol))
            {
                _logger.LogWarning("Invalid symbol provided for cache retrieval: '{Symbol}'", symbol);
                return new List<IBar>();
            }

            // Check for known problematic symbols that cause database issues
            if (ProblematicSymbolsConfig.IsProblematicSymbol(symbol))
            {
                _logger.LogDebug("Skipping cache retrieval for known problematic symbol: {Symbol}", symbol);
                return new List<IBar>(); // Return empty to force API fetch
            }

            if (startDate > endDate)
            {
                _logger.LogWarning("Invalid date range for {Symbol} {TimeFrame}: startDate {StartDate} > endDate {EndDate}",
                    symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
                return new List<IBar>();
            }

            // Prevent future dates
            var today = DateTime.UtcNow.Date;
            if (startDate > today || endDate > today)
            {
                _logger.LogWarning("Future date detected for {Symbol} {TimeFrame}: startDate {StartDate}, endDate {EndDate}, today {Today}",
                    symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), today.ToString("yyyy-MM-dd"));

                // Adjust to valid range
                endDate = endDate > today ? today : endDate;
                startDate = startDate > today ? today.AddDays(-1) : startDate;
            }

            _logger.LogDebug("Retrieving cached bars for {Symbol} {TimeFrame} from {StartDate} to {EndDate}",
                symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

            var cachedBars = await _dbContext.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);
            var bars = cachedBars.Select(cb => cb.ToIBar()).Cast<IBar>().ToList();

            _logger.LogDebug("Found {Count} cached bars for {Symbol} {TimeFrame}", bars.Count, symbol, timeFrame);
            return bars;
        }
        catch (Exception ex)
        {
            // Log as Warning instead of Error to reduce Discord spam
            // Cache failures are non-critical - system can continue with API fetches
            _logger.LogWarning(ex, "Cache retrieval failed for {Symbol} {TimeFrame} from {StartDate} to {EndDate}: {ErrorType} - {ErrorMessage}",
                symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), ex.GetType().Name, ex.Message);

            // Check for specific error types that might indicate data issues
            if (ex.Message.Contains("invalid date") || ex.Message.Contains("date range"))
            {
                _logger.LogInformation("Date range issue detected for {Symbol} {TimeFrame} - this may be related to invalid date parameters", symbol, timeFrame);
            }

            return new List<IBar>();
        }
    }

    public async Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        try
        {
            var barsList = bars.ToList();
            if (!barsList.Any())
            {
                _logger.LogDebug("No bars to cache for {Symbol} {TimeFrame}", symbol, timeFrame);
                return;
            }

            _logger.LogDebug("Caching {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);

            await _dbContext.AddOrUpdateCachedBarsAsync(symbol, timeFrame, barsList);

            _logger.LogInformation("Successfully cached {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching bars for {Symbol} {TimeFrame}: {ErrorMessage}", symbol, timeFrame, ex.Message);

            // Don't throw - caching failures shouldn't break the trading system
            // The system can continue without caching, just with more API calls
            _logger.LogWarning("Continuing without caching for {Symbol} {TimeFrame} due to cache error", symbol, timeFrame);
        }
    }

    public async Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, string timeFrame, DateTime requestedStartDate, DateTime requestedEndDate)
    {
        try
        {
            // Validate inputs to prevent database errors
            if (string.IsNullOrWhiteSpace(symbol))
            {
                _logger.LogWarning("Invalid symbol provided for missing date range calculation: '{Symbol}'", symbol);
                return (requestedStartDate, requestedEndDate);
            }

            // Check for known problematic symbols that cause database issues
            if (ProblematicSymbolsConfig.IsProblematicSymbol(symbol))
            {
                _logger.LogDebug("Skipping cache date range calculation for known problematic symbol: {Symbol}", symbol);
                return (requestedStartDate, requestedEndDate); // Return full range to force API fetch
            }

            if (requestedStartDate > requestedEndDate)
            {
                _logger.LogWarning("Invalid requested date range for {Symbol} {TimeFrame}: startDate {StartDate} > endDate {EndDate}",
                    symbol, timeFrame, requestedStartDate.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));

                // Use safe date range
                var safeEndDate = DateTime.UtcNow.Date.AddDays(-1);
                var safeStartDate = safeEndDate.AddDays(-300);
                return (safeStartDate, safeEndDate);
            }

            var latestCachedDate = await GetLatestCachedDateAsync(symbol, timeFrame);
            var earliestCachedDate = await GetEarliestCachedDateAsync(symbol, timeFrame);

            // If no cached data, fetch entire range
            if (!latestCachedDate.HasValue || !earliestCachedDate.HasValue)
            {
                _logger.LogDebug("No cached data for {Symbol} {TimeFrame}, need to fetch entire range", symbol, timeFrame);
                return (requestedStartDate, requestedEndDate);
            }

            // If cache covers the entire requested range, no fetch needed
            if (earliestCachedDate.Value <= requestedStartDate && latestCachedDate.Value >= requestedEndDate)
            {
                _logger.LogDebug("Cache for {Symbol} {TimeFrame} covers entire requested range", symbol, timeFrame);
                return null;
            }

            // FIXED: Determine what range needs to be fetched with proper logic
            // We need to identify gaps and fetch only what's missing

            // Case 1: Need newer data (requested end is after latest cached)
            if (requestedEndDate > latestCachedDate.Value)
            {
                var fetchStartDate = Math.Max(requestedStartDate.Ticks, latestCachedDate.Value.AddDays(1).Ticks);
                var fetchStart = new DateTime(fetchStartDate);

                // CRITICAL FIX: Prevent startDate > endDate scenario
                if (fetchStart > requestedEndDate)
                {
                    _logger.LogWarning("Fetch start date {FetchStart} would be after end date {EndDate} for {Symbol} {TimeFrame} - using safe date range",
                        fetchStart.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"), symbol, timeFrame);

                    // Use safe date range instead of returning null
                    var safeEndDate = DateTime.UtcNow.Date.AddDays(-1);
                    var safeStartDate = safeEndDate.AddDays(-300);
                    return (safeStartDate, safeEndDate);
                }

                _logger.LogDebug("Need newer data for {Symbol} {TimeFrame}: fetch {StartDate} to {EndDate}",
                    symbol, timeFrame, fetchStart.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));

                return (fetchStart, requestedEndDate);
            }

            // Case 2: Need older data (requested start is before earliest cached)
            if (requestedStartDate < earliestCachedDate.Value)
            {
                var fetchEndDate = Math.Min(requestedEndDate.Ticks, earliestCachedDate.Value.AddDays(-1).Ticks);
                var fetchEnd = new DateTime(fetchEndDate);

                // Ensure we have a valid range
                if (requestedStartDate >= fetchEnd)
                {
                    _logger.LogDebug("No gap to fill for older data for {Symbol} {TimeFrame}", symbol, timeFrame);
                    return null;
                }

                _logger.LogDebug("Need older data for {Symbol} {TimeFrame}: fetch {StartDate} to {EndDate}",
                    symbol, timeFrame, requestedStartDate.ToString("yyyy-MM-dd"), fetchEnd.ToString("yyyy-MM-dd"));

                return (requestedStartDate, fetchEnd);
            }

            // Case 3: Requested range is within cached range but may have gaps
            // For now, assume cache is complete within its range
            _logger.LogDebug("Requested range for {Symbol} {TimeFrame} is within cached range, no fetch needed", symbol, timeFrame);
            return null;
        }
        catch (Exception ex)
        {
            // Log as Warning instead of Error to reduce Discord spam
            // Date range calculation failures are non-critical - system can fetch full range
            _logger.LogWarning(ex, "Date range calculation failed for {Symbol} {TimeFrame}: {ErrorType} - {ErrorMessage}",
                symbol, timeFrame, ex.GetType().Name, ex.Message);

            // On error, fetch entire range to be safe
            return (requestedStartDate, requestedEndDate);
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        try
        {
            return await _dbContext.GetLatestCachedDateAsync(symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest cached date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        try
        {
            return await _dbContext.GetEarliestCachedDateAsync(symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting earliest cached date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task<bool> IsCacheFreshAsync(string symbol, string timeFrame, DateTime requestedEndDate)
    {
        try
        {
            var latestCachedDate = await GetLatestCachedDateAsync(symbol, timeFrame);

            if (!latestCachedDate.HasValue)
                return false;

            // First check: Does cache have data up to the requested end date
            var hasRequiredData = latestCachedDate.Value >= requestedEndDate;

            if (!hasRequiredData)
            {
                _logger.LogDebug("Cache missing required data for {Symbol} {TimeFrame}: latest={LatestDate}, requested={RequestedDate}",
                    symbol, timeFrame, latestCachedDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));
                return false;
            }

            // Second check: Is the latest cached data within staleness threshold
            // Use the latest cached date as the data timestamp for staleness validation
            var dataAge = DateTime.UtcNow - latestCachedDate.Value;
            var isMarketHours = IsMarketHours();

            // Apply market-hours-aware staleness threshold
            var stalenessThreshold = isMarketHours
                ? TimeSpan.FromMinutes(18)  // Market hours: 18 minutes
                : TimeSpan.FromHours(8);    // After hours: 8 hours

            var isDataFresh = dataAge <= stalenessThreshold;

            _logger.LogDebug("Cache freshness for {Symbol} {TimeFrame}: latest={LatestDate}, requested={RequestedDate}, " +
                           "dataAge={DataAge}, threshold={Threshold}, marketHours={IsMarketHours}, fresh={IsFresh}",
                symbol, timeFrame, latestCachedDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"),
                dataAge, stalenessThreshold, isMarketHours, isDataFresh);

            return isDataFresh;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache freshness for {Symbol} {TimeFrame}", symbol, timeFrame);
            return false;
        }
    }

    /// <summary>
    /// Determines if the current time is during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
    /// </summary>
    private bool IsMarketHours()
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
            TimeZoneInfo.FindSystemTimeZoneById("US Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's during market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);

        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        try
        {
            _logger.LogInformation("Starting stock cache maintenance, retaining {RetainDays} days of data", retainDays);

            await _dbContext.CleanupOldDataAsync(retainDays);

            _logger.LogInformation("Stock cache maintenance completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during stock cache maintenance");
            throw;
        }
    }

    public async Task InitializeCacheAsync()
    {
        try
        {
            _logger.LogDebug("Initializing stock cache database");

            await _dbContext.EnsureDatabaseCreatedAsync();

            _logger.LogDebug("Stock cache database initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing stock cache database");
            throw;
        }
    }

    public async Task<IDictionary<string, CacheStats>> GetCacheStatsAsync()
    {
        try
        {
            return await _dbContext.GetCacheStatsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return new Dictionary<string, CacheStats>();
        }
    }
}
