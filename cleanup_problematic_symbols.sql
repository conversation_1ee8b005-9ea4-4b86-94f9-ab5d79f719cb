-- Cleanup script for problematic symbols that cause database caching issues
-- Run this script to remove cached data for symbols that have known database issues
-- NOTE: BRK.A and BRK.B are legitimate symbols and should NOT be cleaned up

-- Remove cached bars for problematic symbols
DELETE FROM "CachedStockBars"
WHERE "Symbol" IN ('BELFB', 'BATRA');

-- Remove cache metadata for problematic symbols
DELETE FROM "StockCacheMetadata"
WHERE "Symbol" IN ('BELFB', 'BATRA');

-- Remove trailing stop records for problematic symbols
DELETE FROM "TrailingStops"
WHERE "Symbol" IN ('BELFB', 'BATRA');

-- Show count of remaining records
SELECT 'CachedStockBars' as TableName, COUNT(*) as RecordCount FROM "CachedStockBars"
UNION ALL
SELECT 'StockCacheMetadata' as TableName, COUNT(*) as RecordCount FROM "StockCacheMetadata"
UNION ALL
SELECT 'TrailingStops' as TableName, COUNT(*) as RecordCount FROM "TrailingStops";

-- Show any remaining problematic symbols (should be 0)
SELECT "Symbol", COUNT(*) as RecordCount
FROM "CachedStockBars"
WHERE "Symbol" IN ('BELFB', 'BATRA')
GROUP BY "Symbol";
