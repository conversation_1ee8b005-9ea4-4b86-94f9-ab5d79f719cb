using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;
using SmaTrendFollower.Interfaces;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Thread-safe implementation of StockBarCacheService using DbContextFactory
/// Fixes the critical threading issues that were causing system failures
/// Integrates with Redis high-frequency cache and circuit breaker for resilience
/// </summary>
public class ThreadSafeStockBarCacheService : IStockBarCacheService
{
    private readonly IDbContextFactory<StockBarCacheDbContext> _contextFactory;
    private readonly IHighFrequencyCache _highFrequencyCache;
    private readonly IDatabaseCircuitBreakerService _circuitBreaker;
    private readonly ILogger<ThreadSafeStockBarCacheService> _logger;

    public ThreadSafeStockBarCacheService(
        IDbContextFactory<StockBarCacheDbContext> contextFactory,
        IHighFrequencyCache highFrequencyCache,
        IDatabaseCircuitBreakerService circuitBreaker,
        ILogger<ThreadSafeStockBarCacheService> logger)
    {
        _contextFactory = contextFactory;
        _highFrequencyCache = highFrequencyCache;
        _circuitBreaker = circuitBreaker;
        _logger = logger;
    }

    public async Task<IReadOnlyList<IBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        try
        {
            // Validate inputs to prevent database errors
            if (string.IsNullOrWhiteSpace(symbol))
            {
                _logger.LogWarning("Invalid symbol provided for cache retrieval: '{Symbol}'", symbol);
                return new List<IBar>();
            }

            // Check for known problematic symbols that cause database issues
            if (ProblematicSymbolsConfig.IsProblematicSymbol(symbol))
            {
                _logger.LogDebug("Skipping cache retrieval for known problematic symbol: {Symbol}", symbol);
                return new List<IBar>(); // Return empty to force API fetch
            }

            if (startDate > endDate)
            {
                _logger.LogWarning("Invalid date range for {Symbol} {TimeFrame}: startDate {StartDate} > endDate {EndDate}",
                    symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
                return new List<IBar>();
            }

            // Prevent future dates
            var today = DateTime.UtcNow.Date;
            if (startDate > today || endDate > today)
            {
                _logger.LogWarning("Future date detected for {Symbol} {TimeFrame}: startDate {StartDate}, endDate {EndDate}, today {Today}",
                    symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), today.ToString("yyyy-MM-dd"));

                // Adjust to valid range
                endDate = endDate > today ? today : endDate;
                startDate = startDate > today ? today.AddDays(-1) : startDate;
            }

            _logger.LogDebug("Retrieving cached bars for {Symbol} {TimeFrame} from {StartDate} to {EndDate}",
                symbol, timeFrame, startDate, endDate);

            // For recent single-day requests, check Redis first for faster access
            if (timeFrame == "Day" && startDate.Date == endDate.Date && endDate.Date >= DateTime.UtcNow.Date.AddDays(-1))
            {
                var recentBar = await _highFrequencyCache.GetBarSnapshotAsync(symbol);
                if (recentBar != null && recentBar.TimeUtc.Date == endDate.Date)
                {
                    _logger.LogDebug("Retrieved recent bar for {Symbol} from Redis cache", symbol);
                    return new List<IBar> { recentBar };
                }
            }

            // Fall back to SQLite for historical data or when Redis cache miss
            using var context = await _contextFactory.CreateDbContextAsync();

            var cachedBars = await context.CachedStockBars
                .Where(b => b.Symbol == symbol &&
                           b.TimeFrame == timeFrame &&
                           b.TimeUtc >= startDate &&
                           b.TimeUtc <= endDate)
                .OrderBy(b => b.TimeUtc)
                .ToListAsync();

            _logger.LogDebug("Found {Count} cached bars for {Symbol} {TimeFrame} from SQLite", cachedBars.Count, symbol, timeFrame);

            var bars = cachedBars.Select(b => b.ToIBar()).ToList();
            return bars;
        }
        catch (Exception ex)
        {
            // Log as Warning instead of Error to reduce Discord spam
            // Cache failures are non-critical - system can continue with API fetches
            _logger.LogWarning(ex, "Cache retrieval failed for {Symbol} {TimeFrame} from {StartDate} to {EndDate}: {ErrorType} - {ErrorMessage}",
                symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), ex.GetType().Name, ex.Message);

            // Check for specific error types that might indicate data issues
            if (ex.Message.Contains("invalid date") || ex.Message.Contains("date range"))
            {
                _logger.LogInformation("Date range issue detected for {Symbol} {TimeFrame} - this may be related to invalid date parameters", symbol, timeFrame);
            }

            return new List<IBar>();
        }
    }

    public async Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        try
        {
            var barsList = bars.ToList();
            if (!barsList.Any())
            {
                _logger.LogDebug("No bars to cache for {Symbol} {TimeFrame}", symbol, timeFrame);
                return;
            }

            _logger.LogDebug("Caching {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);

            // Use circuit breaker for database operations with Redis fallback
            await _circuitBreaker.CacheBarsWithCircuitBreakerAsync(
                symbol,
                timeFrame,
                barsList,
                sqliteOperation: async () =>
                {
                    using var context = await _contextFactory.CreateDbContextAsync();
                    await context.AddOrUpdateCachedBarsAsync(symbol, timeFrame, barsList);
                },
                redisOperation: async () =>
                {
                    // Fallback: Cache recent bars in Redis only
                    if (timeFrame == "Day")
                    {
                        var recentBars = barsList.Where(b => b.TimeUtc >= DateTime.UtcNow.AddDays(-7)).ToList();
                        var batchData = recentBars.ToDictionary(b => $"{symbol}:{b.TimeUtc:yyyy-MM-dd}", b => b);
                        await _highFrequencyCache.CacheBatchBarSnapshotsAsync(batchData, TimeSpan.FromHours(24));
                        _logger.LogInformation("Cached {Count} recent bars for {Symbol} in Redis fallback", recentBars.Count, symbol);
                    }
                });

            _logger.LogInformation("Successfully cached {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching bars for {Symbol} {TimeFrame}: {ErrorMessage}", symbol, timeFrame, ex.Message);

            // Don't throw - caching failures shouldn't break the trading system
            // The system can continue without caching, just with more API calls
            _logger.LogWarning("Continuing without caching for {Symbol} {TimeFrame} due to cache error", symbol, timeFrame);
        }
    }



    public async Task<bool> HasCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var hasData = await context.CachedStockBars
                .AnyAsync(b => b.Symbol == symbol && 
                              b.TimeFrame == timeFrame && 
                              b.TimeUtc >= startDate && 
                              b.TimeUtc <= endDate);

            _logger.LogDebug("Cache check for {Symbol} {TimeFrame}: {HasData}", symbol, timeFrame, hasData);
            return hasData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cached bars for {Symbol} {TimeFrame}", symbol, timeFrame);
            return false;
        }
    }

    public async Task<DateTime?> GetLatestBarDateAsync(string symbol, string timeFrame)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var latestDate = await context.CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame)
                .MaxAsync(b => (DateTime?)b.TimeUtc);

            _logger.LogDebug("Latest bar date for {Symbol} {TimeFrame}: {LatestDate}", symbol, timeFrame, latestDate);
            return latestDate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest bar date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        return await GetLatestBarDateAsync(symbol, timeFrame);
    }

    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var earliestDate = await context.CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame)
                .MinAsync(b => (DateTime?)b.TimeUtc);

            _logger.LogDebug("Earliest bar date for {Symbol} {TimeFrame}: {EarliestDate}", symbol, timeFrame, earliestDate);
            return earliestDate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting earliest bar date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task ClearCacheAsync(string symbol, string timeFrame)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var barsToRemove = context.CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame);

            context.CachedStockBars.RemoveRange(barsToRemove);
            await context.SaveChangesAsync();

            _logger.LogInformation("Cleared cache for {Symbol} {TimeFrame}", symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache for {Symbol} {TimeFrame}", symbol, timeFrame);
            throw;
        }
    }

    public async Task<long> GetCacheSizeAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.CachedStockBars.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache size");
            return 0;
        }
    }

    public async Task<IEnumerable<string>> GetCachedSymbolsAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            return await context.CachedStockBars
                .Select(b => b.Symbol)
                .Distinct()
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cached symbols");
            return Enumerable.Empty<string>();
        }
    }

    public async Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, string timeFrame, DateTime requestedStartDate, DateTime requestedEndDate)
    {
        try
        {
            // Validate inputs to prevent database errors
            if (string.IsNullOrWhiteSpace(symbol))
            {
                _logger.LogWarning("Invalid symbol provided for missing date range calculation: '{Symbol}'", symbol);
                return (requestedStartDate, requestedEndDate);
            }

            // Check for known problematic symbols that cause database issues
            if (ProblematicSymbolsConfig.IsProblematicSymbol(symbol))
            {
                _logger.LogDebug("Skipping cache date range calculation for known problematic symbol: {Symbol}", symbol);
                return (requestedStartDate, requestedEndDate); // Return full range to force API fetch
            }

            if (requestedStartDate > requestedEndDate)
            {
                _logger.LogWarning("Invalid requested date range for {Symbol} {TimeFrame}: startDate {StartDate} > endDate {EndDate}",
                    symbol, timeFrame, requestedStartDate.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));

                // Use safe date range
                var safeEndDate = DateTime.UtcNow.Date.AddDays(-1);
                var safeStartDate = safeEndDate.AddDays(-300);
                return (safeStartDate, safeEndDate);
            }

            using var context = await _contextFactory.CreateDbContextAsync();

            // Get both the earliest and latest cached dates to understand our coverage
            var cachedDateRange = await context.CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame)
                .GroupBy(b => 1)
                .Select(g => new {
                    EarliestDate = g.Min(b => (DateTime?)b.TimeUtc),
                    LatestDate = g.Max(b => (DateTime?)b.TimeUtc),
                    Count = g.Count()
                })
                .FirstOrDefaultAsync();

            if (cachedDateRange?.EarliestDate == null || cachedDateRange?.LatestDate == null)
            {
                // No data cached, need to fetch entire range
                _logger.LogDebug("No cached data for {Symbol} {TimeFrame}, fetching full range {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}",
                    symbol, timeFrame, requestedStartDate, requestedEndDate);
                return (requestedStartDate, requestedEndDate);
            }

            var earliestCached = cachedDateRange.EarliestDate.Value;
            var latestCached = cachedDateRange.LatestDate.Value;
            var cachedCount = cachedDateRange.Count;

            _logger.LogDebug("Cache analysis for {Symbol} {TimeFrame}: {Count} bars from {EarliestDate:yyyy-MM-dd} to {LatestDate:yyyy-MM-dd}, requested: {RequestedStart:yyyy-MM-dd} to {RequestedEnd:yyyy-MM-dd}",
                symbol, timeFrame, cachedCount, earliestCached, latestCached, requestedStartDate, requestedEndDate);

            // CRITICAL FIX: Check if we have sufficient historical data for SMA calculations
            // For daily bars, we need at least 200 bars for SMA200 calculations
            var needsHistoricalData = false;
            var needsRecentData = false;

            // Check if we need more historical data (going backwards)
            if (earliestCached > requestedStartDate || cachedCount < 200)
            {
                needsHistoricalData = true;
                _logger.LogDebug("Need historical data for {Symbol}: earliest cached {EarliestCached:yyyy-MM-dd} > requested start {RequestedStart:yyyy-MM-dd} OR insufficient bars {Count} < 200",
                    symbol, earliestCached, requestedStartDate, cachedCount);
            }

            // Check if we need more recent data (going forwards)
            if (latestCached < requestedEndDate)
            {
                needsRecentData = true;
                _logger.LogDebug("Need recent data for {Symbol}: latest cached {LatestCached:yyyy-MM-dd} < requested end {RequestedEnd:yyyy-MM-dd}",
                    symbol, latestCached, requestedEndDate);
            }

            // If we have sufficient data coverage, no fetch needed
            if (!needsHistoricalData && !needsRecentData)
            {
                _logger.LogDebug("Cache is sufficient for {Symbol} {TimeFrame} with {Count} bars", symbol, timeFrame, cachedCount);
                return null;
            }

            // CRITICAL FIX: When we need historical data, fetch the FULL requested range
            // This ensures we get enough data for SMA calculations instead of just filling small gaps
            DateTime fetchStartDate, fetchEndDate;

            if (needsHistoricalData && cachedCount < 200)
            {
                // We don't have enough bars for SMA calculations - fetch the full requested range
                fetchStartDate = requestedStartDate;
                fetchEndDate = requestedEndDate;
                _logger.LogInformation("Fetching FULL range for {Symbol} {TimeFrame}: {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd} (insufficient bars: {Count} < 200)",
                    symbol, timeFrame, fetchStartDate, fetchEndDate, cachedCount);
            }
            else
            {
                // We have enough bars but need to fill gaps
                fetchStartDate = needsHistoricalData ? requestedStartDate : latestCached.AddDays(1);
                fetchEndDate = needsRecentData ? requestedEndDate : latestCached;
                _logger.LogInformation("Fetching gap fill for {Symbol} {TimeFrame}: {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd} (historical: {Historical}, recent: {Recent})",
                    symbol, timeFrame, fetchStartDate, fetchEndDate, needsHistoricalData, needsRecentData);
            }

            // CRITICAL FIX: Prevent startDate > endDate scenario
            if (fetchStartDate > fetchEndDate)
            {
                _logger.LogWarning("Invalid fetch range for {Symbol} {TimeFrame}: start {Start:yyyy-MM-dd} > end {End:yyyy-MM-dd}, using safe date range",
                    symbol, timeFrame, fetchStartDate, fetchEndDate);

                // Use safe date range: yesterday back 300 days
                var safeEndDate = DateTime.UtcNow.Date.AddDays(-1);
                var safeStartDate = safeEndDate.AddDays(-300);
                return (safeStartDate, safeEndDate);
            }

            return (fetchStartDate, fetchEndDate);
        }
        catch (Exception ex)
        {
            // Log as Warning instead of Error to reduce Discord spam
            // Date range calculation failures are non-critical - system can fetch full range
            _logger.LogWarning(ex, "Date range calculation failed for {Symbol} {TimeFrame}: {ErrorType} - {ErrorMessage}",
                symbol, timeFrame, ex.GetType().Name, ex.Message);

            return (requestedStartDate, requestedEndDate);
        }
    }

    public async Task<bool> IsCacheFreshAsync(string symbol, string timeFrame, DateTime requestedEndDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var latestDate = await context.CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame)
                .MaxAsync(b => (DateTime?)b.TimeUtc);

            if (!latestDate.HasValue)
                return false;

            // First check: Does cache have data up to the requested end date
            var hasRequiredData = latestDate.Value >= requestedEndDate;

            if (!hasRequiredData)
            {
                _logger.LogDebug("Cache missing required data for {Symbol} {TimeFrame}: latest={LatestDate}, requested={RequestedDate}",
                    symbol, timeFrame, latestDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));
                return false;
            }

            // Second check: Is the latest cached data within staleness threshold
            var dataAge = DateTime.UtcNow - latestDate.Value;
            var isMarketHours = IsMarketHours();

            // Apply market-hours-aware staleness threshold
            var stalenessThreshold = isMarketHours
                ? TimeSpan.FromMinutes(18)  // Market hours: 18 minutes
                : TimeSpan.FromHours(8);    // After hours: 8 hours

            var isDataFresh = dataAge <= stalenessThreshold;

            _logger.LogDebug("ThreadSafe cache freshness for {Symbol} {TimeFrame}: latest={LatestDate}, requested={RequestedDate}, " +
                           "dataAge={DataAge}, threshold={Threshold}, marketHours={IsMarketHours}, fresh={IsFresh}",
                symbol, timeFrame, latestDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"),
                dataAge, stalenessThreshold, isMarketHours, isDataFresh);

            return isDataFresh;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache freshness for {Symbol} {TimeFrame}", symbol, timeFrame);
            return false;
        }
    }

    /// <summary>
    /// Determines if the current time is during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
    /// </summary>
    private bool IsMarketHours()
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
            TimeZoneInfo.FindSystemTimeZoneById("US Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's during market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);

        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);
            var oldBars = context.CachedStockBars.Where(b => b.TimeUtc < cutoffDate);

            context.CachedStockBars.RemoveRange(oldBars);
            await context.SaveChangesAsync();

            _logger.LogInformation("Cache maintenance completed for stock bars, removed data older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            throw;
        }
    }

    public async Task InitializeCacheAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.Database.EnsureCreatedAsync();
            _logger.LogDebug("Stock bar cache database initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing cache");
            throw;
        }
    }

    public async Task<IDictionary<string, CacheStats>> GetCacheStatsAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var stats = await context.CachedStockBars
                .GroupBy(b => new { b.Symbol, b.TimeFrame })
                .Select(g => new
                {
                    g.Key.Symbol,
                    g.Key.TimeFrame,
                    BarCount = g.Count(),
                    EarliestDate = g.Min(b => (DateTime?)b.TimeUtc),
                    LatestDate = g.Max(b => (DateTime?)b.TimeUtc),
                    LastUpdated = g.Max(b => b.CachedAt)
                })
                .ToListAsync();

            return stats.ToDictionary(
                s => $"{s.Symbol}_{s.TimeFrame}",
                s => new CacheStats(s.Symbol, s.TimeFrame, s.BarCount, s.EarliestDate, s.LatestDate, s.LastUpdated, 0));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache stats");
            return new Dictionary<string, CacheStats>();
        }
    }
}
